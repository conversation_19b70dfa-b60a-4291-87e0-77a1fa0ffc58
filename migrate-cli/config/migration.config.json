{"version": "1.0.0", "steps": {"package-upgrade": {"name": "package-upgrade", "description": "升级 package.json 依赖", "enabled": true, "order": 1, "required": true}, "dependency-check": {"name": "dependency-check", "description": "检查依赖兼容性", "enabled": true, "order": 2, "required": false}, "code-migration": {"name": "code-migration", "description": "批量迁移代码文件", "enabled": true, "order": 3, "required": true}, "failure-logging": {"name": "failure-logging", "description": "记录失败文件", "enabled": true, "order": 4, "required": false}, "ai-repair": {"name": "ai-repair", "description": "AI 修复失败文件", "enabled": true, "order": 5, "required": false}, "eslint-fix": {"name": "eslint-fix", "description": "ESLint 自动修复", "enabled": true, "order": 6, "required": false}, "build-fix": {"name": "build-fix", "description": "构建项目并修复错误", "enabled": true, "order": 7, "required": false}}, "strategies": {"ai-assisted": {"name": "ai-assisted", "description": "AI 辅助迁移", "priority": 1, "requirements": ["aiApiKey"]}, "documentation-guided": {"name": "documentation-guided", "description": "文档指导迁移", "priority": 2, "requirements": []}}, "filePatterns": {"include": ["**/*.vue", "**/*.js", "**/*.ts"], "exclude": ["node_modules/**", "dist/**", "build/**", "coverage/**"]}, "build": {"defaultCommand": "npm run build", "maxAttempts": 3, "timeout": 300000}, "ai": {"providers": ["openai", "deepseek", "glm"], "defaultProvider": "openai", "maxRetries": 3, "timeout": 60000}, "logging": {"level": "info", "output": "console", "file": "migration.log"}}