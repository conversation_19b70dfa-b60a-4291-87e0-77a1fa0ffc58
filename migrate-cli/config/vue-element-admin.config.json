{"version": "1.0.0", "name": "vue-element-admin", "description": "Vue Element Admin 项目专用迁移配置", "projectDetection": {"packageName": "vue-element-admin", "dependencies": ["element-ui", "vue-router", "vuex"], "files": ["src/layout", "src/views", "src/router"], "vueVersion": "^2.6.0"}, "autoConfig": {"buildCommand": "npm run build:prod", "skipDependencyCheck": false, "skipAIRepair": false, "skipESLint": false, "skipBuild": false, "aiProvider": "deepseek", "verbose": true}, "migrationStrategy": "ai-assisted", "steps": {"package-upgrade": {"name": "package-upgrade", "description": "升级 package.json 依赖", "enabled": true, "order": 1, "required": true, "autoConfig": {"targetVueVersion": "^3.4.0", "targetRouterVersion": "^4.5.0", "targetVuexVersion": "^4.1.0", "replaceElementUI": "element-plus"}}, "dependency-check": {"name": "dependency-check", "description": "检查依赖兼容性", "enabled": true, "order": 2, "required": false, "autoConfig": {"skipOnlineCheck": true}}, "code-migration": {"name": "code-migration", "description": "批量迁移代码文件", "enabled": true, "order": 3, "required": true, "autoConfig": {"useGogocode": true, "plugins": ["gogocode-plugin-vue", "gogocode-plugin-element"]}}, "failure-logging": {"name": "failure-logging", "description": "记录失败文件", "enabled": true, "order": 4, "required": false}, "ai-repair": {"name": "ai-repair", "description": "AI 修复失败文件", "enabled": true, "order": 5, "required": false, "autoConfig": {"provider": "deepseek", "maxRetries": 3}}, "eslint-fix": {"name": "eslint-fix", "description": "ESLint 自动修复", "enabled": true, "order": 6, "required": false}, "build-fix": {"name": "build-fix", "description": "构建项目并修复错误", "enabled": true, "order": 7, "required": false, "autoConfig": {"useAI": true, "maxAttempts": 3}}}, "filePatterns": {"include": ["src/**/*.vue", "src/**/*.js", "src/**/*.ts"], "exclude": ["node_modules/**", "dist/**", "build/**", "coverage/**", "mock/**", "tests/**"]}, "build": {"defaultCommand": "npm run build:prod", "maxAttempts": 3, "timeout": 300000}, "ai": {"providers": ["deepseek", "glm", "openai"], "defaultProvider": "deepseek", "maxRetries": 3, "timeout": 60000, "envKeys": {"deepseek": "DEEPSEEK_API_KEY", "glm": "GLM_API_KEY", "openai": "OPENAI_API_KEY"}}, "logging": {"level": "info", "output": "console", "file": "vue-element-admin-migration.log"}, "customSteps": {"pre-migration": ["backup-project", "install-dependencies"], "post-migration": ["update-router-config", "update-store-config", "update-layout-components", "copy-gogocode-transfer-utils"]}, "backupConfig": {"enabled": true, "backupDir": "migration-backup", "includeNodeModules": false}}