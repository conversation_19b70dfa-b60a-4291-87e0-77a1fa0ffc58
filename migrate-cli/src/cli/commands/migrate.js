const chalk = require('chalk');
const path = require('path');
const { MigrationContext } = require('../../core/context');
const { MigrationPipeline } = require('../../core/pipeline');

/**
 * 完整迁移命令处理器
 */
class MigrateCommand {
  constructor(logger) {
    this.logger = logger;
  }

  /**
   * 执行迁移命令
   */
  async execute(projectPath, options) {
    try {
      this.logger.info('开始执行完整迁移', { projectPath, options });

      // 创建迁移上下文
      const context = new MigrationContext(projectPath, {
        skipDependencyCheck: options.skipDependencyCheck,
        skipAIRepair: options.skipAi,
        skipESLint: options.skipEslint,
        skipBuild: options.skipBuild,
        aiApiKey: options.aiKey,
        buildCommand: options.buildCommand,
        dryRun: options.dryRun,
        verbose: options.verbose
      });

      // 初始化上下文
      await context.initialize();

      // 打印迁移信息
      this.printMigrationInfo(context);

      // 创建迁移流水线
      const pipeline = new MigrationPipeline();
      
      // 开始迁移
      context.startMigration();
      
      // 执行迁移流水线
      await pipeline.execute(context);

      // 完成迁移
      context.completeMigration();

      // 打印结果
      this.printMigrationResult(context);

    } catch (error) {
      this.logger.error('迁移执行失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 打印迁移信息
   */
  printMigrationInfo(context) {
    console.log(chalk.bold.blue('\n🚀 Vue 2 到 Vue 3 统一迁移工具\n'));
    console.log(chalk.gray(`项目路径: ${context.projectPath}`));
    console.log(chalk.gray(`开始时间: ${new Date().toLocaleString()}`));
    console.log(chalk.gray(`模式: ${context.options.dryRun ? '预览模式' : '实际执行'}`));
    
    const vueVersion = context.getData('vueVersion');
    if (vueVersion) {
      console.log(chalk.gray(`Vue 版本: ${vueVersion.version}`));
      if (vueVersion.isVue3) {
        console.log(chalk.yellow('⚠️  检测到 Vue 3 项目，可能不需要迁移'));
      }
    }
    
    console.log('');
  }

  /**
   * 打印迁移结果
   */
  printMigrationResult(context) {
    const summary = context.stats.getSummary();
    const duration = Math.round(summary.duration / 1000);

    console.log('\n' + chalk.bold.green('🎉 Vue 2 到 Vue 3 迁移完成!'));
    
    console.log('\n' + chalk.bold('📊 迁移统计:'));
    console.log(`耗时: ${duration} 秒`);
    console.log(`完成步骤: ${summary.completedSteps}/${summary.totalSteps}`);
    console.log(`错误数量: ${summary.errorCount}`);
    console.log(`警告数量: ${summary.warningCount}`);

    if (summary.filesProcessed > 0) {
      console.log(`文件处理: ${summary.filesSuccess} 成功, ${summary.filesFailed} 失败, ${summary.filesSkipped} 跳过`);
    }

    if (summary.success) {
      console.log(chalk.green('✅ 迁移成功完成'));
    } else {
      console.log(chalk.yellow('⚠️  迁移完成但存在问题，请检查错误日志'));
    }

    // 打印建议
    this.printRecommendations(context);
  }

  /**
   * 打印后续建议
   */
  printRecommendations(context) {
    const recommendations = [];

    if (context.stats.errors.length > 0) {
      recommendations.push('检查错误日志并手动修复剩余问题');
    }

    if (context.getData('failedFiles', []).length > 0) {
      recommendations.push('手动检查迁移失败的文件');
    }

    if (!context.options.dryRun) {
      recommendations.push('运行 npm install 安装新依赖');
      recommendations.push('运行测试确保功能正常');
      recommendations.push('检查 UI 组件是否正确迁移到 Element Plus');
      recommendations.push('更新文档和部署配置');
    }

    if (recommendations.length > 0) {
      console.log('\n' + chalk.bold('💡 后续建议:'));
      recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }
  }
}

module.exports = MigrateCommand; 