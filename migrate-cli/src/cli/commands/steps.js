const chalk = require('chalk');

/**
 * 步骤说明命令处理器
 */
class StepsCommand {
  constructor(logger) {
    this.logger = logger;
  }

  execute() {
    console.log(chalk.bold.blue('\n🚀 Vue 2 到 Vue 3 迁移步骤说明\n'));

    console.log(chalk.bold('传统迁移模式（原地修改）:'));
    const steps = [
      '1. 升级 package.json 依赖 - 将 Vue 相关依赖升级到 Vue 3 版本',
      '2. 检查依赖兼容性 - 检查第三方依赖是否支持 Vue 3',
      '3. 批量迁移代码文件 - 使用 Gogocode 转换 .vue 和 .js 文件',
      '4. 记录失败文件 - 记录转换失败的文件供后续处理',
      '5. AI 修复失败文件 - 使用 AI 自动修复转换失败的文件',
      '6. ESLint 自动修复 - 运行 ESLint 修复格式和语法问题',
      '7. 构建项目并修复错误 - 尝试构建项目并使用 AI 修复构建错误'
    ];

    steps.forEach((step, index) => {
      console.log(chalk.green(`${index + 1}. ${step.split(' - ')[0]}`));
      console.log(chalk.gray(`   ${step.split(' - ')[1]}\n`));
    });

    console.log(chalk.bold('新工程迁移模式（推荐）:'));
    const newSteps = [
      '1. 对比 package.json - 分析新旧工程的依赖差异',
      '2. 迁移组件 - 将 components 目录转换并复制到新工程',
      '3. 迁移视图 - 将 views 目录转换并复制到新工程'
    ];

    newSteps.forEach((step, index) => {
      console.log(chalk.blue(`${index + 1}. ${step.split(' - ')[0]}`));
      console.log(chalk.gray(`   ${step.split(' - ')[1]}\n`));
    });

    console.log(chalk.yellow('💡 提示:'));
    console.log('智能策略分析（推荐）:');
    console.log('- 使用 "vue-migrator analyze" 分析项目并生成迁移策略');
    console.log('- 根据是否有 AI token 自动选择最佳迁移方案');
    console.log('- 生成详细的迁移指导文档');
    console.log('\n传统模式:');
    console.log('- 使用 "vue-migrator migrate" 执行完整迁移');
    console.log('- 使用 "vue-migrator step <number>" 执行单个步骤');
    console.log('\n新工程模式:');
    console.log('- 使用 "vue-migrator migrate-to <old> <new>" 执行完整迁移');
    console.log('- 使用 "vue-migrator compare <old> <new>" 仅对比 package.json');
    console.log('- 使用 "vue-migrator migrate-components <old> <new>" 仅迁移组件');
    console.log('- 使用 "vue-migrator migrate-views <old> <new>" 仅迁移视图');
    console.log('- 使用 --dry-run 选项预览变更而不实际修改文件');
  }
}

module.exports = StepsCommand; 