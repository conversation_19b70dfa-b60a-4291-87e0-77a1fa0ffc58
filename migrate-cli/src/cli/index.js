#!/usr/bin/env node

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');

// 导入命令处理器
const MigrateCommand = require('./commands/migrate');
const StepCommand = require('./commands/step');
const AnalyzeCommand = require('./commands/analyze');
const CompareCommand = require('./commands/compare');
const StepsCommand = require('./commands/steps');

// 导入工具
const { createLogger } = require('../utils/logger');
const { ErrorHandler } = require('../utils/error-handler');

/**
 * Vue 迁移工具 CLI
 */
class VueMigratorCLI {
  constructor() {
    this.program = new Command();
    this.logger = createLogger({ level: 'info' });
    this.errorHandler = new ErrorHandler();
    
    this.setupProgram();
    this.setupCommands();
    this.setupErrorHandling();
  }

  /**
   * 设置主程序
   */
  setupProgram() {
    this.program
      .name('vue-migrator')
      .description('Vue 2 到 Vue 3 统一迁移工具')
      .version('1.0.0')
      .option('--verbose', '显示详细信息')
      .option('--debug', '启用调试模式')
      .hook('preAction', (thisCommand, actionCommand) => {
        // 设置全局选项
        const options = thisCommand.opts();
        if (options.debug) {
          this.logger = createLogger({ level: 'debug' });
        }
        if (options.verbose) {
          this.logger = createLogger({ level: 'info', verbose: true });
        }
      });
  }

  /**
   * 设置命令
   */
  setupCommands() {
    // 完整迁移命令
    this.program
      .command('migrate')
      .description('执行完整的 Vue 2 到 Vue 3 迁移（7个步骤）')
      .argument('[project-path]', '项目路径', process.cwd())
      .option('--skip-dependency-check', '跳过依赖兼容性检查')
      .option('--skip-ai', '跳过 AI 修复步骤')
      .option('--skip-eslint', '跳过 ESLint 自动修复')
      .option('--skip-build', '跳过构建和构建错误修复')
      .option('--ai-key <key>', 'AI API Key (支持 DeepSeek/GLM/OpenAI)')
      .option('--build-command <cmd>', '构建命令', 'npm run build')
      .option('--dry-run', '预览模式，不实际修改文件')
      .action(async (projectPath, options) => {
        try {
          const command = new MigrateCommand(this.logger);
          await command.execute(projectPath, options);
        } catch (error) {
          this.handleError(error);
        }
      });

    // 单步执行命令
    this.program
      .command('step <number> [project-path]')
      .description('执行指定的迁移步骤 (1-7)')
      .option('--ai-key <key>', 'OpenAI API Key')
      .option('--build-command <cmd>', '构建命令', 'npm run build')
      .option('--dry-run', '预览模式，不实际修改文件')
      .action(async (stepNumber, projectPath = process.cwd(), options) => {
        try {
          const command = new StepCommand(this.logger);
          await command.execute(parseInt(stepNumber), projectPath, options);
        } catch (error) {
          this.handleError(error);
        }
      });

    // 策略分析命令
    this.program
      .command('analyze [project-path]')
      .description('分析项目并生成迁移策略和文档')
      .option('--ai-key <key>', 'AI API Key (支持 DeepSeek/GLM/OpenAI)')
      .option('--output <path>', '输出文档路径', 'migration-guide.md')
      .action(async (projectPath = process.cwd(), options) => {
        try {
          const command = new AnalyzeCommand(this.logger);
          await command.execute(projectPath, options);
        } catch (error) {
          this.handleError(error);
        }
      });

    // 对比命令
    this.program
      .command('compare <old-project> <new-project>')
      .description('对比新旧工程的 package.json')
      .option('--verbose', '显示详细信息')
      .action(async (oldProject, newProject, options) => {
        try {
          const command = new CompareCommand(this.logger);
          await command.execute(oldProject, newProject, options);
        } catch (error) {
          this.handleError(error);
        }
      });

    // 步骤说明命令
    this.program
      .command('steps')
      .description('显示迁移步骤说明')
      .action(() => {
        const command = new StepsCommand(this.logger);
        command.execute();
      });

    // 新旧工程迁移命令（保持向后兼容）
    this.program
      .command('migrate-to <old-project> <new-project>')
      .description('从旧 Vue 2 工程迁移到新 Vue 3 工程')
      .option('--compare-only', '仅对比 package.json，不执行迁移')
      .option('--components-only', '仅迁移组件')
      .option('--views-only', '仅迁移视图')
      .option('--dry-run', '预览模式，不实际修改文件')
      .action(async (oldProject, newProject, options) => {
        try {
          // 这里暂时使用旧的实现，后续会重构
          const { UnifiedVueMigrator } = require('../legacy/migrator');
          console.log(chalk.yellow('⚠️  此命令使用旧版实现，建议使用新的 migrate 命令'));
          
          // 调用旧版实现
          const migrator = new UnifiedVueMigrator(oldProject, {
            outputPath: newProject,
            dryRun: options.dryRun,
            verbose: options.verbose
          });
          
          if (options.compareOnly) {
            // 仅对比
            const PackageComparator = require('../dependency/packageComparator');
            const comparator = new PackageComparator(oldProject, newProject);
            const comparison = await comparator.compare();
            console.log(chalk.green('✅ package.json 对比完成！'));
          } else {
            // 执行迁移
            await migrator.migrate();
          }
        } catch (error) {
          this.handleError(error);
        }
      });
  }

  /**
   * 设置错误处理
   */
  setupErrorHandling() {
    // 全局错误处理
    process.on('uncaughtException', (error) => {
      this.handleError(error);
    });

    process.on('unhandledRejection', (reason, promise) => {
      this.handleError(reason);
    });
  }

  /**
   * 处理错误
   */
  handleError(error) {
    this.errorHandler.handle(error);
    
    if (process.env.DEBUG) {
      console.error(chalk.gray(error.stack));
    }
    
    process.exit(1);
  }

  /**
   * 运行 CLI
   */
  run() {
    // 解析命令行参数
    this.program.parse();

    // 如果没有提供命令，显示帮助
    if (!process.argv.slice(2).length) {
      this.program.outputHelp();
    }
  }
}

// 如果直接运行此文件，启动 CLI
if (require.main === module) {
  const cli = new VueMigratorCLI();
  cli.run();
}

module.exports = VueMigratorCLI; 