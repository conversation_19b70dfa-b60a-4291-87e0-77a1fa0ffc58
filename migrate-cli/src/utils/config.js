const fs = require('fs-extra');
const path = require('path');

/**
 * 迁移配置管理类
 * 统一管理所有配置信息
 */
class MigrationConfig {
  constructor(options = {}) {
    this.options = {
      configPath: options.configPath || path.join(__dirname, '../../config/migration.config.json'),
      ...options
    };
    
    this.config = null;
    this.loaded = false;
  }

  /**
   * 加载配置文件
   */
  async load() {
    try {
      if (await fs.pathExists(this.options.configPath)) {
        this.config = await fs.readJson(this.options.configPath);
      } else {
        this.config = this.getDefaultConfig();
        await this.save();
      }
      this.loaded = true;
    } catch (error) {
      console.warn(`无法加载配置文件: ${error.message}`);
      this.config = this.getDefaultConfig();
      this.loaded = true;
    }
  }

  /**
   * 保存配置文件
   */
  async save() {
    try {
      await fs.ensureDir(path.dirname(this.options.configPath));
      await fs.writeJson(this.options.configPath, this.config, { spaces: 2 });
    } catch (error) {
      console.warn(`无法保存配置文件: ${error.message}`);
    }
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig() {
    return {
      version: '1.0.0',
      steps: {
        'package-upgrade': {
          name: 'package-upgrade',
          description: '升级 package.json 依赖',
          enabled: true,
          order: 1,
          required: true
        },
        'dependency-check': {
          name: 'dependency-check',
          description: '检查依赖兼容性',
          enabled: true,
          order: 2,
          required: false
        },
        'code-migration': {
          name: 'code-migration',
          description: '批量迁移代码文件',
          enabled: true,
          order: 3,
          required: true
        },
        'failure-logging': {
          name: 'failure-logging',
          description: '记录失败文件',
          enabled: true,
          order: 4,
          required: false
        },
        'ai-repair': {
          name: 'ai-repair',
          description: 'AI 修复失败文件',
          enabled: true,
          order: 5,
          required: false
        },
        'eslint-fix': {
          name: 'eslint-fix',
          description: 'ESLint 自动修复',
          enabled: true,
          order: 6,
          required: false
        },
        'build-fix': {
          name: 'build-fix',
          description: '构建项目并修复错误',
          enabled: true,
          order: 7,
          required: false
        }
      },
      strategies: {
        'ai-assisted': {
          name: 'ai-assisted',
          description: 'AI 辅助迁移',
          priority: 1,
          requirements: ['aiApiKey']
        },
        'documentation-guided': {
          name: 'documentation-guided',
          description: '文档指导迁移',
          priority: 2,
          requirements: []
        }
      },
      filePatterns: {
        include: ['**/*.vue', '**/*.js', '**/*.ts'],
        exclude: ['node_modules/**', 'dist/**', 'build/**', 'coverage/**']
      },
      build: {
        defaultCommand: 'npm run build',
        maxAttempts: 3,
        timeout: 300000 // 5 minutes
      },
      ai: {
        providers: ['openai', 'deepseek', 'glm'],
        defaultProvider: 'openai',
        maxRetries: 3,
        timeout: 60000 // 1 minute
      },
      logging: {
        level: 'info',
        output: 'console',
        file: 'migration.log'
      }
    };
  }

  /**
   * 获取配置值
   */
  get(key, defaultValue = null) {
    if (!this.loaded) {
      throw new Error('配置未加载，请先调用 load() 方法');
    }
    
    const keys = key.split('.');
    let value = this.config;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return defaultValue;
      }
    }
    
    return value;
  }

  /**
   * 设置配置值
   */
  set(key, value) {
    if (!this.loaded) {
      throw new Error('配置未加载，请先调用 load() 方法');
    }
    
    const keys = key.split('.');
    let current = this.config;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!(k in current) || typeof current[k] !== 'object') {
        current[k] = {};
      }
      current = current[k];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  /**
   * 获取启用的步骤列表
   */
  getEnabledSteps() {
    const steps = this.get('steps', {});
    return Object.values(steps)
      .filter(step => step.enabled)
      .sort((a, b) => a.order - b.order);
  }

  /**
   * 获取步骤配置
   */
  getStepConfig(stepName) {
    return this.get(`steps.${stepName}`);
  }

  /**
   * 获取策略配置
   */
  getStrategyConfig(strategyName) {
    return this.get(`strategies.${strategyName}`);
  }

  /**
   * 验证配置
   */
  validate() {
    const errors = [];
    
    // 检查必需字段
    if (!this.config.version) {
      errors.push('缺少版本信息');
    }
    
    if (!this.config.steps) {
      errors.push('缺少步骤配置');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 合并用户配置
   */
  mergeUserConfig(userConfig) {
    if (!this.loaded) {
      throw new Error('配置未加载，请先调用 load() 方法');
    }
    
    this.config = this.deepMerge(this.config, userConfig);
  }

  /**
   * 深度合并对象
   */
  deepMerge(target, source) {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }
}

module.exports = MigrationConfig; 