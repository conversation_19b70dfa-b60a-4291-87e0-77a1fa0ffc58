const chalk = require('chalk');

/**
 * 迁移错误类型枚举
 */
const ErrorCodes = {
  // 配置相关错误
  CONFIG_LOAD_FAILED: 'CONFIG_LOAD_FAILED',
  CONFIG_INVALID: 'CONFIG_INVALID',
  
  // 项目验证错误
  PROJECT_NOT_FOUND: 'PROJECT_NOT_FOUND',
  PROJECT_NOT_VUE: 'PROJECT_NOT_VUE',
  PROJECT_ALREADY_VUE3: 'PROJECT_ALREADY_VUE3',
  
  // 依赖相关错误
  DEPENDENCY_UPGRADE_FAILED: 'DEPENDENCY_UPGRADE_FAILED',
  DEPENDENCY_CHECK_FAILED: 'DEPENDENCY_CHECK_FAILED',
  INCOMPATIBLE_DEPENDENCY: 'INCOMPATIBLE_DEPENDENCY',
  
  // 代码迁移错误
  CODE_MIGRATION_FAILED: 'CODE_MIGRATION_FAILED',
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  FILE_READ_FAILED: 'FILE_READ_FAILED',
  FILE_WRITE_FAILED: 'FILE_WRITE_FAILED',
  TRANSFORM_FAILED: 'TRANSFORM_FAILED',
  
  // AI 相关错误
  AI_SERVICE_UNAVAILABLE: 'AI_SERVICE_UNAVAILABLE',
  AI_API_KEY_MISSING: 'AI_API_KEY_MISSING',
  AI_REQUEST_FAILED: 'AI_REQUEST_FAILED',
  AI_RESPONSE_INVALID: 'AI_RESPONSE_INVALID',
  
  // 构建相关错误
  BUILD_FAILED: 'BUILD_FAILED',
  BUILD_COMMAND_NOT_FOUND: 'BUILD_COMMAND_NOT_FOUND',
  BUILD_TIMEOUT: 'BUILD_TIMEOUT',
  
  // ESLint 相关错误
  ESLINT_NOT_AVAILABLE: 'ESLINT_NOT_AVAILABLE',
  ESLINT_FIX_FAILED: 'ESLINT_FIX_FAILED',
  
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  PERMISSION_DENIED: 'PERMISSION_DENIED'
};

/**
 * 迁移错误类
 */
class MigrationError extends Error {
  constructor(message, code, step = null, details = null) {
    super(message);
    this.name = 'MigrationError';
    this.code = code;
    this.step = step;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }

  /**
   * 创建配置加载错误
   */
  static configLoadFailed(path, originalError) {
    return new MigrationError(
      `无法加载配置文件: ${path}`,
      ErrorCodes.CONFIG_LOAD_FAILED,
      'config',
      { path, originalError: originalError?.message }
    );
  }

  /**
   * 创建项目验证错误
   */
  static projectNotFound(projectPath) {
    return new MigrationError(
      `项目目录不存在: ${projectPath}`,
      ErrorCodes.PROJECT_NOT_FOUND,
      'validation',
      { projectPath }
    );
  }

  /**
   * 创建项目类型错误
   */
  static projectNotVue(projectPath) {
    return new MigrationError(
      `这不是一个 Vue 项目: ${projectPath}`,
      ErrorCodes.PROJECT_NOT_VUE,
      'validation',
      { projectPath }
    );
  }

  /**
   * 创建代码迁移错误
   */
  static codeMigrationFailed(filePath, originalError) {
    return new MigrationError(
      `代码迁移失败: ${filePath}`,
      ErrorCodes.CODE_MIGRATION_FAILED,
      'code-migration',
      { filePath, originalError: originalError?.message }
    );
  }

  /**
   * 创建 AI 服务错误
   */
  static aiServiceUnavailable(reason) {
    return new MigrationError(
      `AI 服务不可用: ${reason}`,
      ErrorCodes.AI_SERVICE_UNAVAILABLE,
      'ai-repair',
      { reason }
    );
  }

  /**
   * 创建构建错误
   */
  static buildFailed(command, output) {
    return new MigrationError(
      `构建失败: ${command}`,
      ErrorCodes.BUILD_FAILED,
      'build-fix',
      { command, output }
    );
  }

  /**
   * 转换为用户友好的错误信息
   */
  toUserMessage() {
    const messages = {
      [ErrorCodes.CONFIG_LOAD_FAILED]: '配置文件加载失败，请检查配置文件路径和格式',
      [ErrorCodes.PROJECT_NOT_FOUND]: '项目目录不存在，请检查项目路径',
      [ErrorCodes.PROJECT_NOT_VUE]: '这不是一个 Vue 项目，请确保项目包含 Vue 依赖',
      [ErrorCodes.CODE_MIGRATION_FAILED]: '代码迁移过程中发生错误，请检查文件权限和内容',
      [ErrorCodes.AI_SERVICE_UNAVAILABLE]: 'AI 服务不可用，请检查网络连接和 API 配置',
      [ErrorCodes.BUILD_FAILED]: '项目构建失败，请检查构建配置和依赖',
      [ErrorCodes.UNKNOWN_ERROR]: '发生未知错误，请查看详细日志'
    };

    return messages[this.code] || this.message;
  }

  /**
   * 获取错误严重程度
   */
  getSeverity() {
    const criticalErrors = [
      ErrorCodes.CONFIG_LOAD_FAILED,
      ErrorCodes.PROJECT_NOT_FOUND,
      ErrorCodes.PROJECT_NOT_VUE
    ];

    const warningErrors = [
      ErrorCodes.AI_SERVICE_UNAVAILABLE,
      ErrorCodes.ESLINT_NOT_AVAILABLE
    ];

    if (criticalErrors.includes(this.code)) {
      return 'critical';
    } else if (warningErrors.includes(this.code)) {
      return 'warning';
    } else {
      return 'error';
    }
  }
}

/**
 * 错误处理器
 */
class ErrorHandler {
  constructor(options = {}) {
    this.options = {
      verbose: options.verbose || false,
      continueOnError: options.continueOnError || false,
      ...options
    };
    
    this.errors = [];
  }

  /**
   * 处理错误
   */
  handle(error, context = {}) {
    // 确保错误是 MigrationError 实例
    if (!(error instanceof MigrationError)) {
      error = new MigrationError(
        error.message,
        ErrorCodes.UNKNOWN_ERROR,
        context.step,
        { originalError: error.message, stack: error.stack }
      );
    }

    // 记录错误
    this.errors.push({
      error,
      context,
      timestamp: new Date().toISOString()
    });

    // 根据严重程度处理
    const severity = error.getSeverity();
    
    switch (severity) {
      case 'critical':
        this.handleCriticalError(error, context);
        break;
      case 'warning':
        this.handleWarningError(error, context);
        break;
      default:
        this.handleNormalError(error, context);
    }

    return {
      handled: true,
      shouldContinue: this.options.continueOnError || severity === 'warning',
      severity
    };
  }

  /**
   * 处理严重错误
   */
  handleCriticalError(error, context) {
    console.error(chalk.red('❌ 严重错误:'), error.toUserMessage());
    
    if (this.options.verbose) {
      console.error(chalk.gray('详细信息:'), error.details);
      console.error(chalk.gray('步骤:'), error.step);
    }

    // 严重错误总是中断执行
    throw error;
  }

  /**
   * 处理警告错误
   */
  handleWarningError(error, context) {
    console.warn(chalk.yellow('⚠️  警告:'), error.toUserMessage());
    
    if (this.options.verbose) {
      console.warn(chalk.gray('详细信息:'), error.details);
    }
  }

  /**
   * 处理普通错误
   */
  handleNormalError(error, context) {
    console.error(chalk.red('❌ 错误:'), error.toUserMessage());
    
    if (this.options.verbose) {
      console.error(chalk.gray('详细信息:'), error.details);
      console.error(chalk.gray('步骤:'), error.step);
    }
  }

  /**
   * 获取所有错误
   */
  getErrors() {
    return this.errors.map(item => item.error);
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      total: this.errors.length,
      critical: 0,
      error: 0,
      warning: 0
    };

    this.errors.forEach(item => {
      const severity = item.error.getSeverity();
      stats[severity]++;
    });

    return stats;
  }

  /**
   * 清除错误记录
   */
  clear() {
    this.errors = [];
  }

  /**
   * 生成错误报告
   */
  generateReport() {
    const stats = this.getErrorStats();
    
    return {
      summary: {
        total: stats.total,
        critical: stats.critical,
        error: stats.error,
        warning: stats.warning
      },
      errors: this.errors.map(item => ({
        code: item.error.code,
        message: item.error.message,
        userMessage: item.error.toUserMessage(),
        severity: item.error.getSeverity(),
        step: item.error.step,
        timestamp: item.timestamp,
        details: item.error.details
      }))
    };
  }
}

module.exports = {
  MigrationError,
  ErrorHandler,
  ErrorCodes
}; 