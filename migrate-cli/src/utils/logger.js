const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 日志级别枚举
 */
const LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  SILENT: 4
};

/**
 * 日志级别名称映射
 */
const LogLevelNames = {
  [LogLevel.DEBUG]: 'DEBUG',
  [LogLevel.INFO]: 'INFO',
  [LogLevel.WARN]: 'WARN',
  [LogLevel.ERROR]: 'ERROR',
  [LogLevel.SILENT]: 'SILENT'
};

/**
 * 日志级别颜色映射
 */
const LogLevelColors = {
  [LogLevel.DEBUG]: chalk.gray,
  [LogLevel.INFO]: chalk.blue,
  [LogLevel.WARN]: chalk.yellow,
  [LogLevel.ERROR]: chalk.red
};

/**
 * 迁移日志记录器
 */
class MigrationLogger {
  constructor(options = {}) {
    this.options = {
      level: options.level || LogLevel.INFO,
      output: options.output || 'console', // console, file, both
      file: options.file || 'migration.log',
      format: options.format || 'text', // text, json
      verbose: options.verbose || false,
      ...options
    };

    this.logs = [];
    this.fileStream = null;
    
    // 如果输出到文件，初始化文件流
    if (this.options.output === 'file' || this.options.output === 'both') {
      this.initFileStream();
    }
  }

  /**
   * 初始化文件流
   */
  initFileStream() {
    try {
      const logDir = path.dirname(this.options.file);
      fs.ensureDirSync(logDir);
      
      // 创建文件流（这里只是准备，实际写入在 log 方法中）
      this.logFilePath = path.resolve(this.options.file);
    } catch (error) {
      console.warn(`无法初始化日志文件: ${error.message}`);
      this.options.output = 'console';
    }
  }

  /**
   * 记录日志
   */
  log(level, message, data = null, context = {}) {
    // 检查日志级别
    if (level < this.options.level) {
      return;
    }

    const logEntry = {
      timestamp: new Date().toISOString(),
      level: level,
      levelName: LogLevelNames[level],
      message: message,
      data: data,
      context: {
        step: context.step || 'unknown',
        ...context
      }
    };

    // 添加到内存日志
    this.logs.push(logEntry);

    // 输出日志
    this.outputLog(logEntry);
  }

  /**
   * 输出日志
   */
  outputLog(logEntry) {
    const { output, format } = this.options;

    if (output === 'console' || output === 'both') {
      this.outputToConsole(logEntry);
    }

    if (output === 'file' || output === 'both') {
      this.outputToFile(logEntry, format);
    }
  }

  /**
   * 输出到控制台
   */
  outputToConsole(logEntry) {
    const { level, levelName, message, data, context } = logEntry;
    const color = LogLevelColors[level] || chalk.white;
    
    // 构建日志前缀
    const prefix = color(`[${levelName}]`);
    const stepPrefix = context.step ? chalk.gray(`[${context.step}]`) : '';
    
    // 输出主要消息
    console.log(`${prefix} ${stepPrefix} ${message}`);
    
    // 输出详细数据（仅在 verbose 模式下）
    if (data && this.options.verbose) {
      if (typeof data === 'object') {
        console.log(chalk.gray(JSON.stringify(data, null, 2)));
      } else {
        console.log(chalk.gray(data));
      }
    }
  }

  /**
   * 输出到文件
   */
  async outputToFile(logEntry, format) {
    try {
      let content;
      
      if (format === 'json') {
        content = JSON.stringify(logEntry) + '\n';
      } else {
        const { timestamp, levelName, message, context } = logEntry;
        content = `[${timestamp}] [${levelName}] [${context.step}] ${message}\n`;
      }

      await fs.appendFile(this.logFilePath, content);
    } catch (error) {
      console.warn(`无法写入日志文件: ${error.message}`);
    }
  }

  /**
   * 调试日志
   */
  debug(message, data = null, context = {}) {
    this.log(LogLevel.DEBUG, message, data, context);
  }

  /**
   * 信息日志
   */
  info(message, data = null, context = {}) {
    this.log(LogLevel.INFO, message, data, context);
  }

  /**
   * 警告日志
   */
  warn(message, data = null, context = {}) {
    this.log(LogLevel.WARN, message, data, context);
  }

  /**
   * 错误日志
   */
  error(message, data = null, context = {}) {
    this.log(LogLevel.ERROR, message, data, context);
  }

  /**
   * 步骤开始日志
   */
  stepStart(stepName, description = '') {
    this.info(`开始执行步骤: ${stepName}`, { description }, { step: stepName });
  }

  /**
   * 步骤完成日志
   */
  stepComplete(stepName, result = null) {
    this.info(`步骤完成: ${stepName}`, result, { step: stepName });
  }

  /**
   * 步骤失败日志
   */
  stepFailed(stepName, error = null) {
    this.error(`步骤失败: ${stepName}`, { error: error?.message }, { step: stepName });
  }

  /**
   * 进度日志
   */
  progress(current, total, stepName = '') {
    const percentage = Math.round((current / total) * 100);
    this.info(`进度: ${current}/${total} (${percentage}%)`, null, { step: stepName });
  }

  /**
   * 统计日志
   */
  stats(stats, stepName = '') {
    this.info('统计信息', stats, { step: stepName });
  }

  /**
   * 获取所有日志
   */
  getLogs() {
    return this.logs;
  }

  /**
   * 获取指定级别的日志
   */
  getLogsByLevel(level) {
    return this.logs.filter(log => log.level === level);
  }

  /**
   * 获取指定步骤的日志
   */
  getLogsByStep(step) {
    return this.logs.filter(log => log.context.step === step);
  }

  /**
   * 清除内存中的日志
   */
  clear() {
    this.logs = [];
  }

  /**
   * 生成日志报告
   */
  generateReport() {
    const levelStats = {};
    const stepStats = {};

    // 统计各级别日志数量
    Object.values(LogLevel).forEach(level => {
      if (level !== LogLevel.SILENT) {
        levelStats[LogLevelNames[level]] = this.getLogsByLevel(level).length;
      }
    });

    // 统计各步骤日志数量
    this.logs.forEach(log => {
      const step = log.context.step;
      stepStats[step] = (stepStats[step] || 0) + 1;
    });

    return {
      summary: {
        total: this.logs.length,
        levelStats,
        stepStats
      },
      logs: this.logs
    };
  }

  /**
   * 导出日志到文件
   */
  async exportLogs(outputPath, format = 'json') {
    try {
      const report = this.generateReport();
      
      if (format === 'json') {
        await fs.writeJson(outputPath, report, { spaces: 2 });
      } else {
        const content = this.formatLogsAsText(report.logs);
        await fs.writeFile(outputPath, content);
      }

      this.info(`日志已导出到: ${outputPath}`);
    } catch (error) {
      this.error(`导出日志失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 将日志格式化为文本
   */
  formatLogsAsText(logs) {
    return logs.map(log => {
      const { timestamp, levelName, message, context } = log;
      return `[${timestamp}] [${levelName}] [${context.step}] ${message}`;
    }).join('\n');
  }
}

/**
 * 创建日志记录器实例
 */
function createLogger(options = {}) {
  return new MigrationLogger(options);
}

module.exports = {
  MigrationLogger,
  LogLevel,
  LogLevelNames,
  createLogger
}; 