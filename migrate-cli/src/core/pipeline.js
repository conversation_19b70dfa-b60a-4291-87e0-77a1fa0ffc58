const { MigrationStep } = require('./step');
const { PackageUpgradeStep } = require('./steps/package-upgrade');
const { DependencyCheckStep } = require('./steps/dependency-check');
const { CodeMigrationStep } = require('./steps/code-migration');
const { FailureLoggingStep } = require('./steps/failure-logging');
const { AIRepairStep } = require('./steps/ai-repair');
const { ESLintFixStep } = require('./steps/eslint-fix');
const { BuildFixStep } = require('./steps/build-fix');

/**
 * 迁移流水线
 * 使用责任链模式管理迁移步骤
 */
class MigrationPipeline {
  constructor() {
    this.steps = [];
    this.currentStepIndex = 0;
  }

  /**
   * 添加步骤
   */
  addStep(step) {
    if (step instanceof MigrationStep) {
      this.steps.push(step);
    } else {
      throw new Error('步骤必须是 MigrationStep 的实例');
    }
    return this;
  }

  /**
   * 构建默认流水线
   */
  buildDefaultPipeline() {
    return this
      .addStep(new PackageUpgradeStep())
      .addStep(new DependencyCheckStep())
      .addStep(new CodeMigrationStep())
      .addStep(new FailureLoggingStep())
      .addStep(new AIRepairStep())
      .addStep(new ESLintFixStep())
      .addStep(new BuildFixStep());
  }

  /**
   * 根据配置构建流水线
   */
  buildFromConfig(context) {
    const enabledSteps = context.config.getEnabledSteps();
    
    enabledSteps.forEach(stepConfig => {
      const step = this.createStepFromConfig(stepConfig, context);
      if (step) {
        this.addStep(step);
      }
    });

    return this;
  }

  /**
   * 根据配置创建步骤
   */
  createStepFromConfig(stepConfig, context) {
    const stepMap = {
      'package-upgrade': PackageUpgradeStep,
      'dependency-check': DependencyCheckStep,
      'code-migration': CodeMigrationStep,
      'failure-logging': FailureLoggingStep,
      'ai-repair': AIRepairStep,
      'eslint-fix': ESLintFixStep,
      'build-fix': BuildFixStep
    };

    const StepClass = stepMap[stepConfig.name];
    if (StepClass) {
      return new StepClass(stepConfig);
    }

    context.logger.warn(`未知的步骤类型: ${stepConfig.name}`);
    return null;
  }

  /**
   * 执行流水线
   */
  async execute(context) {
    if (this.steps.length === 0) {
      // 如果没有步骤，构建默认流水线
      this.buildDefaultPipeline();
    }

    context.stats.totalSteps = this.steps.length;
    context.logger.info(`开始执行迁移流水线，共 ${this.steps.length} 个步骤`);

    for (let i = 0; i < this.steps.length; i++) {
      const step = this.steps[i];
      this.currentStepIndex = i;

      try {
        // 检查上下文状态
        if (!context.canContinue()) {
          context.logger.warn('迁移已中断，停止执行');
          break;
        }

        // 检查步骤是否应该跳过
        if (this.shouldSkipStep(step, context)) {
          context.logger.info(`跳过步骤: ${step.name}`, { reason: '配置禁用或选项跳过' });
          context.stats.skipStep(step.name, '配置禁用或选项跳过');
          continue;
        }

        // 执行步骤
        context.logger.stepStart(step.name, step.description);
        const result = await step.execute(context);
        
        // 记录步骤结果
        if (result.success) {
          context.stats.completeStep(step.name, result);
          context.logger.stepComplete(step.name, result);
        } else {
          context.stats.failStep(step.name, result.error);
          context.logger.stepFailed(step.name, result.error);
          
          // 如果步骤失败且是必需的，中断流水线
          if (step.required && !context.options.continueOnError) {
            throw new Error(`必需步骤 ${step.name} 执行失败: ${result.error?.message || '未知错误'}`);
          }
        }

      } catch (error) {
        context.stats.failStep(step.name, error);
        context.logger.stepFailed(step.name, error);
        
        // 记录错误
        context.recordError(error, step.name);
        
        // 如果步骤是必需的，中断流水线
        if (step.required && !context.options.continueOnError) {
          throw error;
        }
      }
    }

    context.logger.info('迁移流水线执行完成');
  }

  /**
   * 检查是否应该跳过步骤
   */
  shouldSkipStep(step, context) {
    // 检查步骤配置
    const stepConfig = context.config.getStepConfig(step.name);
    if (stepConfig && !stepConfig.enabled) {
      return true;
    }

    // 检查选项
    const skipOptions = {
      'dependency-check': context.options.skipDependencyCheck,
      'ai-repair': context.options.skipAIRepair,
      'eslint-fix': context.options.skipESLint,
      'build-fix': context.options.skipBuild
    };

    return skipOptions[step.name] || false;
  }

  /**
   * 获取当前步骤
   */
  getCurrentStep() {
    if (this.currentStepIndex >= 0 && this.currentStepIndex < this.steps.length) {
      return this.steps[this.currentStepIndex];
    }
    return null;
  }

  /**
   * 获取步骤列表
   */
  getSteps() {
    return this.steps;
  }

  /**
   * 获取步骤数量
   */
  getStepCount() {
    return this.steps.length;
  }

  /**
   * 重置流水线
   */
  reset() {
    this.steps = [];
    this.currentStepIndex = 0;
  }

  /**
   * 获取流水线状态
   */
  getStatus() {
    return {
      totalSteps: this.steps.length,
      currentStepIndex: this.currentStepIndex,
      currentStep: this.getCurrentStep()?.name || null,
      completed: this.currentStepIndex >= this.steps.length
    };
  }
}

module.exports = {
  MigrationPipeline
}; 