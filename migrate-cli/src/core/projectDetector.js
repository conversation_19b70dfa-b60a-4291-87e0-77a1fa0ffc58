const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 项目类型检测器
 * 自动识别项目类型并应用相应的预设配置
 */
class ProjectDetector {
  constructor(projectPath) {
    this.projectPath = path.resolve(projectPath);
    this.packageJson = null;
    this.detectedPreset = null;
  }

  /**
   * 检测项目类型
   */
  async detectProject() {
    try {
      // 读取 package.json
      const packageJsonPath = path.join(this.projectPath, 'package.json');
      if (!await fs.pathExists(packageJsonPath)) {
        throw new Error('未找到 package.json 文件');
      }

      this.packageJson = await fs.readJson(packageJsonPath);

      // 检测各种项目类型
      const detectors = [
        this.detectVueElementAdmin.bind(this),
        this.detectVue2Project.bind(this),
        this.detectVue3Project.bind(this)
      ];

      for (const detector of detectors) {
        const result = await detector();
        if (result) {
          this.detectedPreset = result;
          return result;
        }
      }

      return {
        type: 'unknown',
        name: 'unknown',
        description: '未知项目类型',
        confidence: 0
      };

    } catch (error) {
      throw new Error(`项目检测失败: ${error.message}`);
    }
  }

  /**
   * 检测 Vue Element Admin 项目
   */
  async detectVueElementAdmin() {
    const indicators = {
      packageName: this.packageJson.name === 'vue-element-admin',
      dependencies: this.checkDependencies(['element-ui', 'vue-router', 'vuex']),
      structure: await this.checkProjectStructure(['src/layout', 'src/views', 'src/router']),
      vueVersion: this.checkVueVersion('^2.6.0'),
      scripts: this.checkScripts(['build:prod', 'build:stage'])
    };

    const confidence = this.calculateConfidence(indicators);

    if (confidence >= 0.7) {
      return {
        type: 'vue-element-admin',
        name: 'Vue Element Admin',
        description: 'Vue Element Admin 管理系统模板',
        confidence,
        indicators,
        configFile: 'vue-element-admin.config.json'
      };
    }

    return null;
  }

  /**
   * 检测 Vue 2 项目
   */
  async detectVue2Project() {
    const vueVersion = this.getVueVersion();
    if (vueVersion && vueVersion.startsWith('2.')) {
      return {
        type: 'vue2',
        name: 'Vue 2 Project',
        description: 'Vue 2 项目',
        confidence: 0.8,
        configFile: 'migration.config.json'
      };
    }
    return null;
  }

  /**
   * 检测 Vue 3 项目
   */
  async detectVue3Project() {
    const vueVersion = this.getVueVersion();
    if (vueVersion && vueVersion.startsWith('3.')) {
      return {
        type: 'vue3',
        name: 'Vue 3 Project',
        description: 'Vue 3 项目（可能不需要迁移）',
        confidence: 0.9,
        configFile: null
      };
    }
    return null;
  }

  /**
   * 检查依赖是否存在
   */
  checkDependencies(requiredDeps) {
    const allDeps = {
      ...this.packageJson.dependencies,
      ...this.packageJson.devDependencies
    };

    const foundDeps = requiredDeps.filter(dep => allDeps[dep]);
    return foundDeps.length / requiredDeps.length;
  }

  /**
   * 检查项目结构
   */
  async checkProjectStructure(requiredPaths) {
    const existingPaths = [];
    
    for (const requiredPath of requiredPaths) {
      const fullPath = path.join(this.projectPath, requiredPath);
      if (await fs.pathExists(fullPath)) {
        existingPaths.push(requiredPath);
      }
    }

    return existingPaths.length / requiredPaths.length;
  }

  /**
   * 检查 Vue 版本
   */
  checkVueVersion(expectedVersion) {
    const vueVersion = this.getVueVersion();
    if (!vueVersion) return false;

    // 简单的版本匹配
    if (expectedVersion.startsWith('^2.')) {
      return vueVersion.startsWith('2.');
    } else if (expectedVersion.startsWith('^3.')) {
      return vueVersion.startsWith('3.');
    }

    return false;
  }

  /**
   * 获取 Vue 版本
   */
  getVueVersion() {
    return this.packageJson.dependencies?.vue || 
           this.packageJson.devDependencies?.vue;
  }

  /**
   * 检查脚本命令
   */
  checkScripts(requiredScripts) {
    const scripts = this.packageJson.scripts || {};
    const foundScripts = requiredScripts.filter(script => scripts[script]);
    return foundScripts.length / requiredScripts.length;
  }

  /**
   * 计算置信度
   */
  calculateConfidence(indicators) {
    const weights = {
      packageName: 0.3,
      dependencies: 0.25,
      structure: 0.25,
      vueVersion: 0.15,
      scripts: 0.05
    };

    let totalScore = 0;
    let totalWeight = 0;

    for (const [key, value] of Object.entries(indicators)) {
      if (weights[key] !== undefined) {
        const score = typeof value === 'boolean' ? (value ? 1 : 0) : value;
        totalScore += score * weights[key];
        totalWeight += weights[key];
      }
    }

    return totalWeight > 0 ? totalScore / totalWeight : 0;
  }

  /**
   * 加载预设配置
   */
  async loadPresetConfig() {
    if (!this.detectedPreset || !this.detectedPreset.configFile) {
      return null;
    }

    const configPath = path.join(__dirname, '../../config', this.detectedPreset.configFile);
    
    if (await fs.pathExists(configPath)) {
      return await fs.readJson(configPath);
    }

    return null;
  }

  /**
   * 打印检测结果
   */
  printDetectionResult() {
    if (!this.detectedPreset) {
      console.log(chalk.yellow('⚠️  未能识别项目类型'));
      return;
    }

    console.log(chalk.green('✅ 项目检测完成'));
    console.log(chalk.blue(`项目类型: ${this.detectedPreset.name}`));
    console.log(chalk.gray(`描述: ${this.detectedPreset.description}`));
    console.log(chalk.gray(`置信度: ${Math.round(this.detectedPreset.confidence * 100)}%`));

    if (this.detectedPreset.indicators) {
      console.log(chalk.gray('\n检测指标:'));
      for (const [key, value] of Object.entries(this.detectedPreset.indicators)) {
        const score = typeof value === 'boolean' ? (value ? '✅' : '❌') : 
                     `${Math.round(value * 100)}%`;
        console.log(chalk.gray(`  ${key}: ${score}`));
      }
    }
  }

  /**
   * 获取检测结果
   */
  getDetectionResult() {
    return this.detectedPreset;
  }
}

module.exports = ProjectDetector;
