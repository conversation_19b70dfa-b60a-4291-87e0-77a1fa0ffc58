const { MigrationStep } = require('../step');

/**
 * 代码迁移步骤
 * 负责批量迁移代码文件
 */
class CodeMigrationStep extends MigrationStep {
  constructor(config = {}) {
    super({
      name: 'code-migration',
      description: '批量迁移代码文件',
      order: 3,
      required: true,
      ...config
    });
  }

  async execute(context) {
    // TODO: 实现代码迁移逻辑
    context.logger.info('代码迁移步骤（待实现）');
    return this.createSuccessResult({ message: '代码迁移步骤待实现' });
  }
}

module.exports = {
  CodeMigrationStep
}; 