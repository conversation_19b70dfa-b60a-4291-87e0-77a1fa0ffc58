const { MigrationStep } = require('../step');
const PackageUpgrader = require('../../dependency/packageUpgrader');

/**
 * 包升级步骤
 * 负责升级 package.json 中的依赖
 */
class PackageUpgradeStep extends MigrationStep {
  constructor(config = {}) {
    super({
      name: 'package-upgrade',
      description: '升级 package.json 依赖',
      order: 1,
      required: true,
      ...config
    });
  }

  /**
   * 执行包升级
   */
  async execute(context) {
    try {
      context.logger.info('开始升级 package.json 依赖');

      // 检查是否为预览模式
      if (context.options.dryRun) {
        context.logger.info('预览模式：跳过实际文件修改');
        return this.createSuccessResult({
          mode: 'preview',
          message: '预览模式，未实际修改文件'
        });
      }

      // 创建包升级器
      const upgrader = new PackageUpgrader(context.projectPath);
      
      // 执行升级
      const result = await upgrader.upgrade();

      // 记录结果
      context.logger.info('包升级完成', {
        changes: result.changes?.length || 0,
        upgraded: result.upgraded || 0
      });

      // 将结果存储到上下文中
      context.setData('packageUpgradeResult', result);

      return this.createSuccessResult(result);

    } catch (error) {
      context.logger.error('包升级失败', { error: error.message });
      return this.createFailureResult(error);
    }
  }

  /**
   * 验证步骤
   */
  async validate(context) {
    // 检查项目路径
    if (!context.projectPath) {
      return {
        valid: false,
        error: '项目路径未设置'
      };
    }

    // 检查 package.json 是否存在
    const packageJsonPath = require('path').join(context.projectPath, 'package.json');
    const fs = require('fs-extra');
    
    if (!await fs.pathExists(packageJsonPath)) {
      return {
        valid: false,
        error: 'package.json 文件不存在'
      };
    }

    return { valid: true };
  }
}

module.exports = {
  PackageUpgradeStep
}; 