const { MigrationStep } = require('../step');

/**
 * 依赖检查步骤
 * 负责检查依赖兼容性
 */
class DependencyCheckStep extends MigrationStep {
  constructor(config = {}) {
    super({
      name: 'dependency-check',
      description: '检查依赖兼容性',
      order: 2,
      required: false,
      ...config
    });
  }

  async execute(context) {
    // TODO: 实现依赖检查逻辑
    context.logger.info('依赖检查步骤（待实现）');
    return this.createSuccessResult({ message: '依赖检查步骤待实现' });
  }
}

module.exports = {
  DependencyCheckStep
}; 