const { MigrationStep } = require('../step');

/**
 * 失败记录步骤
 * 负责记录失败文件
 */
class FailureLoggingStep extends MigrationStep {
  constructor(config = {}) {
    super({
      name: 'failure-logging',
      description: '记录失败文件',
      order: 4,
      required: false,
      ...config
    });
  }

  async execute(context) {
    // TODO: 实现失败记录逻辑
    context.logger.info('失败记录步骤（待实现）');
    return this.createSuccessResult({ message: '失败记录步骤待实现' });
  }
}

module.exports = {
  FailureLoggingStep
}; 