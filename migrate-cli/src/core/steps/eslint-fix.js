const { MigrationStep } = require('../step');

/**
 * ESLint 修复步骤
 * 负责 ESLint 自动修复
 */
class ESLintFixStep extends MigrationStep {
  constructor(config = {}) {
    super({
      name: 'eslint-fix',
      description: 'ESLint 自动修复',
      order: 6,
      required: false,
      ...config
    });
  }

  async execute(context) {
    // TODO: 实现 ESLint 修复逻辑
    context.logger.info('ESLint 修复步骤（待实现）');
    return this.createSuccessResult({ message: 'ESLint 修复步骤待实现' });
  }
}

module.exports = {
  ESLintFixStep
}; 