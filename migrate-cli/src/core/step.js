/**
 * 迁移步骤基类
 * 定义所有迁移步骤的通用接口
 */
class MigrationStep {
  constructor(config = {}) {
    this.name = config.name || this.constructor.name;
    this.description = config.description || '执行迁移步骤';
    this.order = config.order || 0;
    this.enabled = config.enabled !== false;
    this.required = config.required || false;
    this.config = config;
  }

  /**
   * 执行步骤
   * 子类必须实现此方法
   */
  async execute(context) {
    throw new Error('子类必须实现 execute 方法');
  }

  /**
   * 验证步骤是否可以执行
   */
  async validate(context) {
    // 默认验证：检查上下文是否有效
    if (!context || !context.projectPath) {
      return {
        valid: false,
        error: '无效的迁移上下文'
      };
    }

    return { valid: true };
  }

  /**
   * 步骤执行前的准备工作
   */
  async beforeExecute(context) {
    // 验证步骤
    const validation = await this.validate(context);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error
      };
    }

    // 记录步骤开始
    context.logger.debug(`步骤 ${this.name} 开始执行`, {
      description: this.description,
      order: this.order
    });

    return { success: true };
  }

  /**
   * 步骤执行后的清理工作
   */
  async afterExecute(context, result) {
    // 记录步骤完成
    context.logger.debug(`步骤 ${this.name} 执行完成`, {
      success: result.success,
      result: result.result
    });

    return result;
  }

  /**
   * 包装执行过程
   */
  async executeWithWrapper(context) {
    try {
      // 执行前准备
      const beforeResult = await this.beforeExecute(context);
      if (!beforeResult.success) {
        return beforeResult;
      }

      // 执行步骤
      const result = await this.execute(context);

      // 执行后清理
      return await this.afterExecute(context, result);

    } catch (error) {
      context.logger.error(`步骤 ${this.name} 执行异常`, { error: error.message });
      return {
        success: false,
        error: error
      };
    }
  }

  /**
   * 检查步骤是否应该跳过
   */
  shouldSkip(context) {
    // 检查步骤是否启用
    if (!this.enabled) {
      return { skip: true, reason: '步骤被禁用' };
    }

    // 检查上下文状态
    if (!context.canContinue()) {
      return { skip: true, reason: '迁移已中断' };
    }

    return { skip: false };
  }

  /**
   * 获取步骤信息
   */
  getInfo() {
    return {
      name: this.name,
      description: this.description,
      order: this.order,
      enabled: this.enabled,
      required: this.required
    };
  }

  /**
   * 设置步骤配置
   */
  setConfig(config) {
    this.config = { ...this.config, ...config };
    
    // 更新属性
    if (config.name) this.name = config.name;
    if (config.description) this.description = config.description;
    if (config.order !== undefined) this.order = config.order;
    if (config.enabled !== undefined) this.enabled = config.enabled;
    if (config.required !== undefined) this.required = config.required;
  }

  /**
   * 获取步骤配置
   */
  getConfig() {
    return this.config;
  }

  /**
   * 创建成功结果
   */
  createSuccessResult(data = null) {
    return {
      success: true,
      result: data,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 创建失败结果
   */
  createFailureResult(error, data = null) {
    return {
      success: false,
      error: error,
      data: data,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 创建跳过结果
   */
  createSkipResult(reason = '步骤被跳过') {
    return {
      success: true,
      skipped: true,
      reason: reason,
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = {
  MigrationStep
}; 