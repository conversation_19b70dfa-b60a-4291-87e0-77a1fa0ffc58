const path = require('path');
const { MigrationConfig } = require('../utils/config');
const { <PERSON><PERSON>r<PERSON><PERSON><PERSON> } = require('../utils/error-handler');
const { createLogger } = require('../utils/logger');
const { ProjectValidator, OptionsValidator } = require('../utils/validators');

/**
 * 迁移状态枚举
 */
const MigrationState = {
  INITIALIZED: 'initialized',
  VALIDATING: 'validating',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

/**
 * 迁移统计信息
 */
class MigrationStats {
  constructor() {
    this.startTime = null;
    this.endTime = null;
    this.duration = 0;
    
    this.totalSteps = 0;
    this.completedSteps = 0;
    this.failedSteps = 0;
    this.skippedSteps = 0;
    
    this.filesProcessed = 0;
    this.filesSuccess = 0;
    this.filesFailed = 0;
    this.filesSkipped = 0;
    
    this.errors = [];
    this.warnings = [];
    
    this.stepResults = {};
  }

  /**
   * 开始迁移
   */
  start() {
    this.startTime = new Date();
  }

  /**
   * 结束迁移
   */
  end() {
    this.endTime = new Date();
    this.duration = this.endTime.getTime() - this.startTime.getTime();
  }

  /**
   * 记录步骤完成
   */
  completeStep(stepName, result = null) {
    this.completedSteps++;
    this.stepResults[stepName] = {
      status: 'completed',
      result,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 记录步骤失败
   */
  failStep(stepName, error = null) {
    this.failedSteps++;
    this.stepResults[stepName] = {
      status: 'failed',
      error: error?.message || 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 记录步骤跳过
   */
  skipStep(stepName, reason = null) {
    this.skippedSteps++;
    this.stepResults[stepName] = {
      status: 'skipped',
      reason,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 记录文件处理结果
   */
  recordFileResult(success, skipped = false) {
    this.filesProcessed++;
    if (skipped) {
      this.filesSkipped++;
    } else if (success) {
      this.filesSuccess++;
    } else {
      this.filesFailed++;
    }
  }

  /**
   * 记录错误
   */
  recordError(error) {
    this.errors.push({
      message: error.message,
      code: error.code,
      step: error.step,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录警告
   */
  recordWarning(warning) {
    this.warnings.push({
      message: warning.message,
      step: warning.step,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 获取统计摘要
   */
  getSummary() {
    return {
      duration: this.duration,
      totalSteps: this.totalSteps,
      completedSteps: this.completedSteps,
      failedSteps: this.failedSteps,
      skippedSteps: this.skippedSteps,
      filesProcessed: this.filesProcessed,
      filesSuccess: this.filesSuccess,
      filesFailed: this.filesFailed,
      filesSkipped: this.filesSkipped,
      errorCount: this.errors.length,
      warningCount: this.warnings.length,
      success: this.failedSteps === 0 && this.errors.length === 0
    };
  }

  /**
   * 重置统计
   */
  reset() {
    this.startTime = null;
    this.endTime = null;
    this.duration = 0;
    this.totalSteps = 0;
    this.completedSteps = 0;
    this.failedSteps = 0;
    this.skippedSteps = 0;
    this.filesProcessed = 0;
    this.filesSuccess = 0;
    this.filesFailed = 0;
    this.filesSkipped = 0;
    this.errors = [];
    this.warnings = [];
    this.stepResults = {};
  }
}

/**
 * 迁移上下文类
 * 在整个迁移过程中传递状态和数据
 */
class MigrationContext {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = this.normalizeOptions(options);
    
    // 初始化核心组件
    this.config = new MigrationConfig();
    this.errorHandler = new ErrorHandler({
      verbose: this.options.verbose,
      continueOnError: true
    });
    this.logger = createLogger({
      level: this.options.verbose ? 'debug' : 'info',
      output: 'console',
      verbose: this.options.verbose
    });
    
    // 初始化验证器
    this.projectValidator = new ProjectValidator(this.projectPath);
    this.optionsValidator = new OptionsValidator(this.options);
    
    // 初始化状态
    this.state = MigrationState.INITIALIZED;
    this.stats = new MigrationStats();
    
    // 存储中间数据
    this.data = {
      packageJson: null,
      vueVersion: null,
      projectStructure: null,
      failedFiles: [],
      migrationStrategy: null,
      analysisResult: null
    };
  }

  /**
   * 标准化选项
   */
  normalizeOptions(options) {
    return {
      skipDependencyCheck: options.skipDependencyCheck || false,
      skipAIRepair: options.skipAIRepair || false,
      skipESLint: options.skipESLint || false,
      skipBuild: options.skipBuild || false,
      aiApiKey: options.aiApiKey || process.env.OPENAI_API_KEY,
      buildCommand: options.buildCommand || 'npm run build',
      dryRun: options.dryRun || false,
      verbose: options.verbose || false,
      ...options
    };
  }

  /**
   * 初始化上下文
   */
  async initialize() {
    try {
      this.logger.info('初始化迁移上下文', { projectPath: this.projectPath });
      
      // 加载配置
      await this.config.load();
      
      // 验证配置
      const configValidator = require('../utils/validators').ConfigValidator;
      const configValidation = new configValidator(this.config.config).validate();
      if (!configValidation.valid) {
        throw new Error(`配置验证失败: ${configValidation.errors.join(', ')}`);
      }
      
      // 验证选项
      const optionsValidation = this.optionsValidator.validate();
      if (!optionsValidation.valid) {
        throw new Error(`选项验证失败: ${optionsValidation.errors.join(', ')}`);
      }
      
      // 验证项目
      await this.validateProject();
      
      this.state = MigrationState.INITIALIZED;
      this.logger.info('迁移上下文初始化完成');
      
    } catch (error) {
      this.state = MigrationState.FAILED;
      this.logger.error('迁移上下文初始化失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 验证项目
   */
  async validateProject() {
    this.state = MigrationState.VALIDATING;
    this.logger.info('开始验证项目');
    
    try {
      // 验证项目基本结构
      const projectValidation = await this.projectValidator.validateProject();
      this.data.projectValidation = projectValidation;
      
      // 验证 Vue 版本
      const vueVersion = await this.projectValidator.validateVueVersion();
      this.data.vueVersion = vueVersion;
      
      // 验证项目结构
      const projectStructure = await this.projectValidator.validateProjectStructure();
      this.data.projectStructure = projectStructure;
      
      // 验证文件权限
      await this.projectValidator.validateFilePermissions();
      
      // 读取 package.json
      const packageJson = await this.projectValidator.validatePackageJson();
      this.data.packageJson = packageJson;
      
      this.logger.info('项目验证完成', {
        vueVersion: vueVersion.version,
        isVue3: vueVersion.isVue3,
        canMigrate: vueVersion.canMigrate
      });
      
      if (vueVersion.warning) {
        this.logger.warn(vueVersion.warning);
      }
      
    } catch (error) {
      this.state = MigrationState.FAILED;
      this.logger.error('项目验证失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 设置迁移状态
   */
  setState(state) {
    this.state = state;
    this.logger.debug(`迁移状态变更: ${state}`);
  }

  /**
   * 获取迁移状态
   */
  getState() {
    return this.state;
  }

  /**
   * 设置数据
   */
  setData(key, value) {
    this.data[key] = value;
    this.logger.debug(`设置数据: ${key}`, { value });
  }

  /**
   * 获取数据
   */
  getData(key, defaultValue = null) {
    return this.data[key] !== undefined ? this.data[key] : defaultValue;
  }

  /**
   * 添加失败文件
   */
  addFailedFile(fileInfo) {
    this.data.failedFiles.push(fileInfo);
    this.logger.debug('添加失败文件', { file: fileInfo.file });
  }

  /**
   * 获取失败文件列表
   */
  getFailedFiles() {
    return this.data.failedFiles;
  }

  /**
   * 清除失败文件列表
   */
  clearFailedFiles() {
    this.data.failedFiles = [];
  }

  /**
   * 记录错误
   */
  recordError(error, step = null) {
    this.errorHandler.handle(error, { step });
    this.stats.recordError(error);
    this.logger.error('记录错误', { error: error.message, step });
  }

  /**
   * 记录警告
   */
  recordWarning(warning, step = null) {
    this.stats.recordWarning(warning);
    this.logger.warn('记录警告', { warning: warning.message, step });
  }

  /**
   * 开始迁移
   */
  startMigration() {
    this.state = MigrationState.RUNNING;
    this.stats.start();
    this.logger.info('开始迁移');
  }

  /**
   * 完成迁移
   */
  completeMigration() {
    this.state = MigrationState.COMPLETED;
    this.stats.end();
    this.logger.info('迁移完成', this.stats.getSummary());
  }

  /**
   * 失败迁移
   */
  failMigration(error) {
    this.state = MigrationState.FAILED;
    this.stats.end();
    this.recordError(error);
    this.logger.error('迁移失败', { error: error.message });
  }

  /**
   * 取消迁移
   */
  cancelMigration(reason = '用户取消') {
    this.state = MigrationState.CANCELLED;
    this.stats.end();
    this.logger.warn('迁移被取消', { reason });
  }

  /**
   * 获取迁移报告
   */
  generateReport() {
    const summary = this.stats.getSummary();
    const errorReport = this.errorHandler.generateReport();
    
    return {
      timestamp: new Date().toISOString(),
      projectPath: this.projectPath,
      state: this.state,
      options: this.options,
      summary,
      errors: errorReport,
      stepResults: this.stats.stepResults,
      data: this.data
    };
  }

  /**
   * 检查是否可以继续
   */
  canContinue() {
    return this.state === MigrationState.RUNNING || this.state === MigrationState.VALIDATING;
  }

  /**
   * 检查是否已完成
   */
  isCompleted() {
    return this.state === MigrationState.COMPLETED;
  }

  /**
   * 检查是否失败
   */
  isFailed() {
    return this.state === MigrationState.FAILED;
  }

  /**
   * 检查是否被取消
   */
  isCancelled() {
    return this.state === MigrationState.CANCELLED;
  }
}

module.exports = {
  MigrationContext,
  MigrationState,
  MigrationStats
}; 