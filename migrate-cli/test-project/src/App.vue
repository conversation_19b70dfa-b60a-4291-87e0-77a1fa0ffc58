<template>
  <div id="app">
    <el-container>
      <el-header>
        <nav-bar />
      </el-header>
      <el-main>
        <router-view />
      </el-main>
      <el-footer>
        <footer-component />
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import NavBar from '@/components/layout/NavBar.vue'
import FooterComponent from '@/components/layout/FooterComponent.vue'

export default {
  name: 'App',
  components: {
    NavBar,
    FooterComponent
  },
  metaInfo: {
    title: 'Vue 2 Test Project',
    meta: [
      { name: 'description', content: 'A comprehensive Vue 2 test project for migration' }
    ]
  }
}
</script>

<style>
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

.el-header {
  background-color: #409EFF;
  color: white;
  line-height: 60px;
}

.el-footer {
  background-color: #f5f5f5;
  color: #666;
  text-align: center;
  line-height: 60px;
}
</style> 