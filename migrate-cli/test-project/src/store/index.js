import Vue from 'vue'
import Vuex from 'vuex'
import user from './modules/user'
import app from './modules/app'
import data from './modules/data'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    // 全局状态
    loading: false,
    error: null,
    notifications: []
  },
  mutations: {
    SET_LOADING(state, loading) {
      state.loading = loading
    },
    SET_ERROR(state, error) {
      state.error = error
    },
    ADD_NOTIFICATION(state, notification) {
      state.notifications.push(notification)
    },
    REMOVE_NOTIFICATION(state, id) {
      const index = state.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        state.notifications.splice(index, 1)
      }
    }
  },
  actions: {
    setLoading({ commit }, loading) {
      commit('SET_LOADING', loading)
    },
    setError({ commit }, error) {
      commit('SET_ERROR', error)
    },
    addNotification({ commit }, notification) {
      const id = Date.now()
      commit('ADD_NOTIFICATION', { ...notification, id })
      setTimeout(() => {
        commit('REMOVE_NOTIFICATION', id)
      }, 5000)
    },
    clearError({ commit }) {
      commit('SET_ERROR', null)
    }
  },
  getters: {
    isLoading: state => state.loading,
    error: state => state.error,
    notifications: state => state.notifications
  },
  modules: {
    user,
    app,
    data
  }
}) 