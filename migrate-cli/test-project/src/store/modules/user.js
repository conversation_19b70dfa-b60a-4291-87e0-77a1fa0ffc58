const state = {
  user: null,
  isAuthenticated: false,
  permissions: [],
  profile: {}
}

const mutations = {
  SET_USER(state, user) {
    state.user = user
    state.isAuthenticated = !!user
  },
  SET_PERMISSIONS(state, permissions) {
    state.permissions = permissions
  },
  SET_PROFILE(state, profile) {
    state.profile = profile
  },
  LOGOUT(state) {
    state.user = null
    state.isAuthenticated = false
    state.permissions = []
    state.profile = {}
  }
}

const actions = {
  login({ commit }, credentials) {
    return new Promise((resolve, reject) => {
      // 模拟登录
      setTimeout(() => {
        const user = {
          id: 1,
          username: credentials.username,
          email: '<EMAIL>',
          role: 'admin'
        }
        commit('SET_USER', user)
        commit('SET_PERMISSIONS', ['read', 'write', 'admin'])
        localStorage.setItem('isAuthenticated', 'true')
        resolve(user)
      }, 1000)
    })
  },
  logout({ commit }) {
    commit('LOGOUT')
    localStorage.removeItem('isAuthenticated')
  },
  updateProfile({ commit }, profile) {
    commit('SET_PROFILE', profile)
  }
}

const getters = {
  currentUser: state => state.user,
  isAuthenticated: state => state.isAuthenticated,
  hasPermission: state => permission => state.permissions.includes(permission),
  userRole: state => state.user ? state.user.role : null
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
} 