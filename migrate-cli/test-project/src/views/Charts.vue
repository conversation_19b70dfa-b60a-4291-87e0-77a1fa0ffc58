<template>
  <div class="charts">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <div slot="header">
            <span>Charts and Data Visualization</span>
          </div>
          
          <!-- V-Charts -->
          <el-row :gutter="20" style="margin-bottom: 20px;">
            <el-col :span="12">
              <el-card>
                <div slot="header">
                  <span>V-Charts Line Chart</span>
                </div>
                <ve-line :data="lineChartData" :settings="lineChartSettings" />
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <div slot="header">
                  <span>V-Charts Bar Chart</span>
                </div>
                <ve-bar :data="barChartData" :settings="barChartSettings" />
              </el-card>
            </el-col>
          </el-row>
          
          <!-- Vue ECharts -->
          <el-row :gutter="20" style="margin-bottom: 20px;">
            <el-col :span="12">
              <el-card>
                <div slot="header">
                  <span>Vue ECharts Pie Chart</span>
                </div>
                <v-chart :options="pieChartOptions" style="height: 300px;" />
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <div slot="header">
                  <span>Vue ECharts Radar Chart</span>
                </div>
                <v-chart :options="radarChartOptions" style="height: 300px;" />
              </el-card>
            </el-col>
          </el-row>
          
          <!-- Vue Chart.js -->
          <el-row :gutter="20" style="margin-bottom: 20px;">
            <el-col :span="12">
              <el-card>
                <div slot="header">
                  <span>Vue Chart.js Doughnut</span>
                </div>
                <canvas ref="doughnutChart"></canvas>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <div slot="header">
                  <span>Vue ApexCharts</span>
                </div>
                <apexchart
                  type="area"
                  height="300"
                  :options="apexChartOptions"
                  :series="apexChartSeries"
                />
              </el-card>
            </el-col>
          </el-row>
          
          <!-- Vue Highcharts -->
          <el-row :gutter="20" style="margin-bottom: 20px;">
            <el-col :span="24">
              <el-card>
                <div slot="header">
                  <span>Vue Highcharts</span>
                </div>
                <highcharts :options="highchartsOptions"></highcharts>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import VeLine from 'v-charts/lib/line.common'
import VeBar from 'v-charts/lib/bar.common'
import VChart from 'vue-echarts'
import Chart from 'chart.js'
import VueApexCharts from 'vue-apexcharts'
import Highcharts from 'vue-highcharts'

export default {
  name: 'Charts',
  components: {
    VeLine,
    VeBar,
    VChart,
    VueApexCharts,
    Highcharts
  },
  data() {
    return {
      lineChartData: {
        columns: ['date', 'sales', 'profit'],
        rows: [
          { date: '2024-01-01', sales: 100, profit: 20 },
          { date: '2024-01-02', sales: 200, profit: 40 },
          { date: '2024-01-03', sales: 150, profit: 30 },
          { date: '2024-01-04', sales: 300, profit: 60 }
        ]
      },
      lineChartSettings: {
        yAxisName: ['Sales', 'Profit']
      },
      barChartData: {
        columns: ['category', 'value'],
        rows: [
          { category: 'Category A', value: 100 },
          { category: 'Category B', value: 200 },
          { category: 'Category C', value: 150 },
          { category: 'Category D', value: 300 }
        ]
      },
      barChartSettings: {
        yAxisName: ['Value']
      },
      pieChartOptions: {
        title: {
          text: 'Sales Distribution'
        },
        series: [{
          name: 'Sales',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 335, name: 'Direct' },
            { value: 310, name: 'Email' },
            { value: 234, name: 'Affiliate' },
            { value: 135, name: 'Video' },
            { value: 1548, name: 'Search' }
          ]
        }]
      },
      radarChartOptions: {
        title: {
          text: 'Performance Radar'
        },
        radar: {
          indicator: [
            { name: 'Sales', max: 6000 },
            { name: 'Administration', max: 16000 },
            { name: 'Information Technology', max: 30000 },
            { name: 'Customer Support', max: 38000 },
            { name: 'Development', max: 52000 },
            { name: 'Marketing', max: 25000 }
          ]
        },
        series: [{
          name: 'Budget vs spending',
          type: 'radar',
          data: [{
            value: [4200, 3000, 20000, 35000, 50000, 18000],
            name: 'Allocated Budget'
          }]
        }]
      },
      apexChartOptions: {
        chart: {
          type: 'area',
          height: 300
        },
        xaxis: {
          categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
        },
        colors: ['#409EFF']
      },
      apexChartSeries: [{
        name: 'Sales',
        data: [31, 40, 28, 51, 42, 109]
      }],
      highchartsOptions: {
        chart: {
          type: 'column'
        },
        title: {
          text: 'Monthly Average Rainfall'
        },
        xAxis: {
          categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
        },
        yAxis: {
          title: {
            text: 'Rainfall (mm)'
          }
        },
        series: [{
          name: 'Tokyo',
          data: [49.9, 71.5, 106.4, 129.2, 144.0, 176.0]
        }, {
          name: 'New York',
          data: [83.6, 78.8, 98.5, 93.4, 106.0, 84.5]
        }]
      }
    }
  },
  mounted() {
    this.initChartJS()
  },
  methods: {
    initChartJS() {
      const ctx = this.$refs.doughnutChart.getContext('2d')
      new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['Red', 'Blue', 'Yellow', 'Green'],
          datasets: [{
            data: [12, 19, 3, 5],
            backgroundColor: [
              '#FF6384',
              '#36A2EB',
              '#FFCE56',
              '#4BC0C0'
            ]
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      })
    }
  }
}
</script>

<style scoped>
.charts {
  padding: 20px;
}

canvas {
  height: 300px;
}
</style> 