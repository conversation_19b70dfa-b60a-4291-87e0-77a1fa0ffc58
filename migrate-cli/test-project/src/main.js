import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import VueI18n from 'vue-i18n'
import Vue<PERSON>eta from 'vue-meta'
import VueMoment from 'vue-moment'
import VueProgressbar from 'vue-progressbar'
import VueNotification from 'vue-notification'
import VueToasted from 'vue-toasted'
import VueLoadingOverlay from 'vue-loading-overlay'
import PortalVue from 'portal-vue'
import VueFragment from 'vue-fragment'
import VueObserveVisibility from 'vue-observe-visibility'
import VueResize from 'vue-resize'
import VueClickaway from 'vue-clickaway'
import VueOutsideEvents from 'vue-outside-events'
import VueFocus from 'vue-focus'
import VueHotkey from 'vue-hotkey'
import VueShortkey from 'vue-shortkey'
import VueTouch from 'vue-touch'
import VueGesture from 'vue-gesture'
import VueAnalytics from 'vue-analytics'
import VueGtag from 'vue-gtag'
import VueSocialSharing from 'vue-social-sharing'
import VueCookies from 'vue-cookies'
import VueLs from 'vue-ls'
import VueLocalstorage from 'vue-localstorage'
import VueSessionstorage from 'vue-sessionstorage'
import VueAxios from 'vue-axios'
import axios from 'axios'
import VueResource from 'vue-resource'
import VueSocketIo from 'vue-socket.io'
import VueNativeWebsocket from 'vue-native-websocket'
import VueSse from 'vue-sse'
import VueWorker from 'vue-worker'
import VueWebWorkers from 'vue-web-workers'
import VueAsyncComputed from 'vue-async-computed'
import VueAsyncData from 'vue-async-data'
import VueWait from 'vue-wait'
import VuePromised from 'vue-promised'
import VueConcurrency from 'vue-concurrency'
import VueRx from 'vue-rx'
import VueObserve from 'vue-observe'
import VueReactiveStorage from 'vue-reactive-storage'
import VueStash from 'vue-stash'
import VueSharedState from 'vue-shared-state'
import VueKindergarten from 'vue-kindergarten'
import VueAcl from 'vue-acl'
import VueGates from 'vue-gates'
import VuePermissions from 'vue-permissions'
import VueAuth from 'vue-auth'
import VueAuthenticate from 'vue-authenticate'
import VueJwtAuth from 'vue-jwt-auth'
import VueSocialAuth from 'vue-social-auth'

// 使用插件
Vue.use(ElementUI)
Vue.use(VueI18n)
Vue.use(VueMeta)
Vue.use(VueMoment)
Vue.use(VueProgressbar, {
  color: '#bffaf3',
  failedColor: '#874b4b',
  thickness: '3px',
  transition: {
    speed: '0.2s',
    opacity: '0.6s',
    termination: 300
  },
  autoRevert: true,
  location: 'left',
  inverse: false
})
Vue.use(VueNotification)
Vue.use(VueToasted, {
  iconPack: 'fontawesome',
  duration: 3000
})
Vue.use(VueLoadingOverlay)
Vue.use(PortalVue)
Vue.use(VueFragment.Plugin)
Vue.use(VueObserveVisibility)
Vue.use(VueResize)
Vue.use(VueClickaway)
Vue.use(VueOutsideEvents)
Vue.use(VueFocus)
Vue.use(VueHotkey)
Vue.use(VueShortkey)
Vue.use(VueTouch, { name: 'v-touch' })
Vue.use(VueGesture)
Vue.use(VueAnalytics, {
  id: 'UA-12345678-1'
})
Vue.use(VueGtag, {
  config: { id: 'GA_MEASUREMENT_ID' }
})
Vue.use(VueSocialSharing)
Vue.use(VueCookies)
Vue.use(VueLs, { namespace: 'vuejs__', name: 'ls', storage: 'local' })
Vue.use(VueLocalstorage)
Vue.use(VueSessionstorage)
Vue.use(VueAxios, axios)
Vue.use(VueResource)
Vue.use(VueSocketIo, 'http://localhost:3000')
Vue.use(VueNativeWebsocket, 'ws://localhost:8080', {
  store: store,
  format: 'json'
})
Vue.use(VueSse)
Vue.use(VueWorker)
Vue.use(VueWebWorkers)
Vue.use(VueAsyncComputed)
Vue.use(VueAsyncData)
Vue.use(VueWait)
Vue.use(VuePromised)
Vue.use(VueConcurrency)
Vue.use(VueRx)
Vue.use(VueObserve)
Vue.use(VueReactiveStorage)
Vue.use(VueStash)
Vue.use(VueSharedState)
Vue.use(VueKindergarten)
Vue.use(VueAcl)
Vue.use(VueGates)
Vue.use(VuePermissions)
Vue.use(VueAuth)
Vue.use(VueAuthenticate)
Vue.use(VueJwtAuth)
Vue.use(VueSocialAuth)

// 全局配置
Vue.config.productionTip = false

// 全局混入
Vue.mixin({
  created() {
    console.log('Component created:', this.$options.name)
  }
})

// 全局指令
Vue.directive('focus', {
  inserted: function (el) {
    el.focus()
  }
})

// 全局过滤器
Vue.filter('capitalize', function (value) {
  if (!value) return ''
  value = value.toString()
  return value.charAt(0).toUpperCase() + value.slice(1)
})

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app') 