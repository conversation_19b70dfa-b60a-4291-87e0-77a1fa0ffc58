<template>
  <el-menu
    :default-active="activeIndex"
    class="nav-menu"
    mode="horizontal"
    router
    background-color="#409EFF"
    text-color="#fff"
    active-text-color="#ffd04b"
  >
    <el-menu-item index="/">
      <i class="el-icon-s-home"></i>
      Home
    </el-menu-item>
    <el-menu-item index="/dashboard">
      <i class="el-icon-s-data"></i>
      Dashboard
    </el-menu-item>
    <el-menu-item index="/components">
      <i class="el-icon-s-grid"></i>
      Components
    </el-menu-item>
    <el-menu-item index="/charts">
      <i class="el-icon-s-marketing"></i>
      Charts
    </el-menu-item>
    <el-menu-item index="/forms">
      <i class="el-icon-edit"></i>
      Forms
    </el-menu-item>
    <el-menu-item index="/tables">
      <i class="el-icon-s-order"></i>
      Tables
    </el-menu-item>
    <el-menu-item index="/calendar">
      <i class="el-icon-date"></i>
      Calendar
    </el-menu-item>
    <el-menu-item index="/editor">
      <i class="el-icon-edit-outline"></i>
      Editor
    </el-menu-item>
    <el-menu-item index="/upload">
      <i class="el-icon-upload"></i>
      Upload
    </el-menu-item>
    
    <div class="nav-right">
      <el-dropdown @command="handleCommand">
        <span class="el-dropdown-link">
          <i class="el-icon-user"></i>
          {{ currentUser ? currentUser.username : 'Guest' }}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="profile">Profile</el-dropdown-item>
          <el-dropdown-item command="settings">Settings</el-dropdown-item>
          <el-dropdown-item divided command="logout">Logout</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </el-menu>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'NavBar',
  data() {
    return {
      activeIndex: '/'
    }
  },
  computed: {
    ...mapGetters('user', ['currentUser', 'isAuthenticated'])
  },
  watch: {
    $route(to) {
      this.activeIndex = to.path
    }
  },
  methods: {
    ...mapActions('user', ['logout']),
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$message.info('Profile clicked')
          break
        case 'settings':
          this.$message.info('Settings clicked')
          break
        case 'logout':
          this.logout()
          this.$router.push('/auth')
          this.$message.success('Logged out successfully')
          break
      }
    }
  },
  mounted() {
    this.activeIndex = this.$route.path
  }
}
</script>

<style scoped>
.nav-menu {
  border-bottom: none;
}

.nav-right {
  float: right;
  height: 60px;
  line-height: 60px;
  margin-right: 20px;
}

.el-dropdown-link {
  color: #fff;
  cursor: pointer;
}

.el-dropdown-link:hover {
  color: #ffd04b;
}
</style> 