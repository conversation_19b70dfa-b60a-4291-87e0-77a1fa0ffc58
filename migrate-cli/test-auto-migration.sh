#!/bin/bash

# Vue Element Admin 自动迁移测试脚本
# 用于快速验证自动迁移功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}"
    echo "=================================="
    echo "Vue Element Admin 自动迁移测试"
    echo "=================================="
    echo -e "${NC}"
}

print_step() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查项目路径
check_project() {
    local project_path="../vue-element-admin"
    
    if [ ! -d "$project_path" ]; then
        print_error "未找到 vue-element-admin 项目目录: $project_path"
        echo "请确保项目位于正确的位置"
        exit 1
    fi
    
    if [ ! -f "$project_path/package.json" ]; then
        print_error "未找到 package.json 文件"
        exit 1
    fi
    
    print_success "找到 vue-element-admin 项目"
}

# 测试项目检测功能
test_project_detection() {
    print_step "测试项目检测功能..."
    
    echo "运行命令: node bin/vue-migrator.js auto ../vue-element-admin --dry-run --verbose"
    echo ""
    
    # 运行项目检测（预览模式）
    if node bin/vue-migrator.js auto ../vue-element-admin --dry-run --verbose 2>&1 | head -50; then
        print_success "项目检测测试完成"
    else
        print_error "项目检测测试失败"
        return 1
    fi
}

# 测试快速脚本
test_quick_script() {
    print_step "测试快速脚本..."
    
    if [ -x "./scripts/migrate-vue-element-admin.sh" ]; then
        echo "运行命令: ./scripts/migrate-vue-element-admin.sh --dry-run ../vue-element-admin"
        echo ""
        
        if timeout 30s ./scripts/migrate-vue-element-admin.sh --dry-run ../vue-element-admin 2>&1 | head -30; then
            print_success "快速脚本测试完成"
        else
            print_warning "快速脚本测试超时或失败（这是正常的）"
        fi
    else
        print_warning "快速脚本不可执行，跳过测试"
    fi
}

# 显示可用命令
show_available_commands() {
    print_step "可用的自动迁移命令:"
    echo ""
    echo "1. 基本自动迁移:"
    echo "   node bin/vue-migrator.js auto ../vue-element-admin"
    echo ""
    echo "2. 预览模式（推荐先试用）:"
    echo "   node bin/vue-migrator.js auto ../vue-element-admin --dry-run"
    echo ""
    echo "3. 跳过 AI 步骤（更快）:"
    echo "   node bin/vue-migrator.js auto ../vue-element-admin --skip-ai"
    echo ""
    echo "4. 使用快速脚本:"
    echo "   ./scripts/migrate-vue-element-admin.sh ../vue-element-admin"
    echo ""
    echo "5. 查看帮助:"
    echo "   node bin/vue-migrator.js auto --help"
    echo ""
}

# 显示环境配置建议
show_env_setup() {
    print_step "环境配置建议:"
    echo ""
    echo "为了获得最佳体验，建议设置 AI API Key:"
    echo ""
    echo "# DeepSeek（推荐）"
    echo "export DEEPSEEK_API_KEY=\"your_deepseek_api_key\""
    echo ""
    echo "# 或者 GLM"
    echo "export GLM_API_KEY=\"your_glm_api_key\""
    echo ""
    echo "# 或者 OpenAI"
    echo "export OPENAI_API_KEY=\"your_openai_api_key\""
    echo ""
}

# 主函数
main() {
    print_header
    
    # 检查项目
    check_project
    echo ""
    
    # 测试项目检测
    test_project_detection
    echo ""
    
    # 测试快速脚本
    test_quick_script
    echo ""
    
    # 显示可用命令
    show_available_commands
    
    # 显示环境配置
    show_env_setup
    
    print_success "自动迁移功能测试完成！"
    echo ""
    echo "现在你可以使用上述命令进行实际的项目迁移。"
    echo "建议先使用 --dry-run 选项预览变更。"
}

# 运行主函数
main "$@"
